def parse_user_codes(filename):
    """
    解析用户编码文件，返回包含所有用户编码的字符串列表。
    
    参数:
        filename (str): 要解析的文件路径
        
    返回:
        list: 包含所有用户编码的字符串列表，如果文件未找到则返回空列表
    """
    user_codes = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line in file:
                # 清除首尾空白字符
                cleaned_line = line.strip()
                
                # 忽略空行
                if cleaned_line:
                    user_codes.append(cleaned_line)
                    
    except FileNotFoundError:
        # 文件未找到时返回空列表
        return []
    
    return user_codes


# 测试代码（可选）
if __name__ == "__main__":
    # 测试函数
    test_codes = parse_user_codes("user_code.txt")
    print(f"找到 {len(test_codes)} 个用户编码:")
    for code in test_codes:
        print(f"  - {code}")