import requests
import time
import json
import logging
from datetime import datetime
from typing import List, Dict

# ==================== 配置 ====================

# API 配置
BASE_URL = "https://oa.imile.com/oa/asset/sync/user"
HEADERS = {
    "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
    "Authorization": "Bearer cc1083f1-8ac7-41f1-8f23-8a0abb25d81c",
    "Accept": "*/*",
    "Host": "oa.imile.com",
    "Connection": "keep-alive"
}

# 用户代码列表（按顺序处理）
USER_CODES = [
    "990059", "990004", "2102347701", "2103165501", "2102645", "2101932701",
    "2103235901", "210824301", "2102882", "210979", "2102633", "2101061",
    "2102558", "2103174701", "2104643", "2101173201", "210639", "210811601",
    "100112", "2103169001", "210735801", "2101242601", "2104664", "2101094",
    "2102458001", "2102392701", "2103995101", "210788301", "2101056", "2101038",
    "2102403", "2104068", "2101438401", "2104285201", "2104308901", "2101184201",
    "2101657001", "2104076001", "2102791301", "2102791601", "2104338501", "210702101",
    "990061", "2103309301", "2103192401", "2103038901", "2102046301", "2101234",
    "2102203", "2102472", "2101980", "2104054401", "2104604", "2101891",
    "2101933001", "2104058", "210815", "990031", "2102009801", "9900006",
    "210042", "990032", "2101801501", "2104059", "210184", "990100",
    "2101238", "990088", "2102365401", "2102068", "2101664101", "2101896301",
    "2102353701", "2101896", "2102272", "2101389", "2103399", "2104789",
    "2102317301", "2101850001", "2103018101", "2103063701", "2102791101", "210889301",
    "990110", "990026", "2103682801", "2103164501", "2104705", "210797201",
    "2103911201", "2101179", "210661701", "210985901", "2101172901", "210703301",
    "210937901", "2101838801", "2102123", "2102504", "9900018", "2101432",
    "990001", "210814", "2104863", "2101206101", "2102411", "2103495",
    "2105835", "210680", "210854301", "2105038"
]

# 跳过用户代码
SKIP_CODES = {"990059"}

# 延时配置（秒）
DELAY_BETWEEN_REQUESTS = 1
MAX_RETRIES = 3

# 输出文件
LOG_FILE = "api_execution.log"
RESPONSES_FILE = "responses.json"
REPORT_FILE = "summary_report.txt"

# ==================== 日志配置 ====================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# ==================== 主程序 ====================
def make_request(user_code: str) -> Dict:
    url = f"{BASE_URL}?userCode={user_code}"
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            return {
                "userCode": user_code,
                "status": response.status_code,
                "success": 200 <= response.status_code < 300,
                "response": response.text,
                "attempt": attempt,
                "error": None
            }
        except requests.exceptions.RequestException as e:
            logging.warning(f"请求失败 (第 {attempt} 次): 用户 {user_code} - {e}")
            if attempt == MAX_RETRIES:
                return {
                    "userCode": user_code,
                    "status": None,
                    "success": False,
                    "response": None,
                    "attempt": attempt,
                    "error": str(e)
                }
            time.sleep(2 ** attempt)  # 指数退避
    return {}

def main():
    # 过滤跳过的用户代码，并从指定位置开始
    start_index = USER_CODES.index("990004") if "990004" in USER_CODES else 0
    filtered_codes = [code for code in USER_CODES[start_index:] if code not in SKIP_CODES]

    total = len(filtered_codes)
    success_count = 0
    failed_count = 0
    results = []

    logging.info(f"开始批量执行 API 请求，共 {total} 个用户代码。")

    start_time = datetime.now()

    for i, user_code in enumerate(filtered_codes, 1):
        print(f"\r处理进度: {i}/{total} ({(i/total)*100:.1f}%) - 当前用户: {user_code}", end="", flush=True)
        logging.info(f"正在处理用户: {user_code}")

        result = make_request(user_code)
        results.append(result)

        if result["success"]:
            success_count += 1
            logging.info(f"✅ 成功 - 用户 {user_code} | 状态码: {result['status']}")
        else:
            failed_count += 1
            logging.error(f"❌ 失败 - 用户 {user_code} | 错误: {result['error'] or f'HTTP {result['status']}'}")

        time.sleep(DELAY_BETWEEN_REQUESTS)

    # 结束进度显示
    print("\n")

    end_time = datetime.now()
    duration = end_time - start_time

    # 保存响应结果
    with open(RESPONSES_FILE, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    # 生成摘要报告
    report = f"""
==================== 执行摘要报告 ====================
开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
总耗时: {duration}
总请求数: {total}
成功数: {success_count}
失败数: {failed_count}
成功率: {success_count / total * 100:.1f}%
结果文件: {RESPONSES_FILE}
日志文件: {LOG_FILE}
====================================================
"""
    with open(REPORT_FILE, 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)
    logging.info("批量 API 请求任务已完成。")

if __name__ == "__main__":
    main()